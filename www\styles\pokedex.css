/* pokedex.css: <PERSON><PERSON><PERSON> für den neuen Pokedex Main Screen */
.pokedex-header {
    /* Spezifische Stile für den Pokedex-Header, falls nötig */
}
.pokedex-count {
    color: var(--standard-text-color);
    font-size: 1.1rem;
    font-weight: 500;
    text-shadow: none;
}
.pokedex-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
    padding: 14px;
}

.pokedex-empty {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: var(--standard-text-color);
}

.pokedex-empty p {
    margin: 10px 0;
    font-size: 1.1rem;
}

.pokedex-empty p:first-child {
    font-weight: 600;
    font-size: 1.3rem;
    color: var(--primary-color);
}
.pokedex-card {
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.09);
    padding: 8px 10px;
    font-size: 0.98rem;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    box-sizing: border-box;
    min-height: 90px;
}


/* Type label backgrounds by type */
.type-label.type-bug    { background: var(--type-bug); }
.type-label.type-dark   { background: var(--type-dark); }
.type-label.type-dragon { background: var(--type-dragon); }
.type-label.type-electric { background: var(--type-electric); }
.type-label.type-fairy  { background: var(--type-fairy); }
.type-label.type-fighting { background: var(--type-fighting); }
.type-label.type-fire   { background: var(--type-fire); }
.type-label.type-flying { background: var(--type-flying); }
.type-label.type-ghost  { background: var(--type-ghost); }
.type-label.type-grass  { background: var(--type-grass); }
.type-label.type-ground { background: var(--type-ground); }
.type-label.type-ice    { background: var(--type-ice); }
.type-label.type-normal { background: var(--type-normal); }
.type-label.type-poison { background: var(--type-poison); }
.type-label.type-psychic { background: var(--type-psychic); }
.type-label.type-rock   { background: var(--type-rock); }
.type-label.type-steel  { background: var(--type-steel); }
.type-label.type-water  { background: var(--type-water); }
.type-label {
    color: #fff;
    border-radius: 8px;
    padding: 3px 12px;
    font-size: 1rem;
    text-shadow: 0 2px 6px rgba(0,0,0,0.25);
    display: inline-block;
    font-weight: 500;
    width: auto;
    min-width: 0;
    box-sizing: border-box;
}

/* Card backgrounds by type (single) */
.pokedex-card.type-bg-bug    { background: var(--type-bug); }
.pokedex-card.type-bg-dark   { background: var(--type-dark); }
.pokedex-card.type-bg-dragon { background: var(--type-dragon); }
.pokedex-card.type-bg-electric { background: var(--type-electric); }
.pokedex-card.type-bg-fairy  { background: var(--type-fairy); }
.pokedex-card.type-bg-fighting { background: var(--type-fighting); }
.pokedex-card.type-bg-fire   { background: var(--type-fire); }
.pokedex-card.type-bg-flying { background: var(--type-flying); }
.pokedex-card.type-bg-ghost  { background: var(--type-ghost); }
.pokedex-card.type-bg-grass  { background: var(--type-grass); }
.pokedex-card.type-bg-ground { background: var(--type-ground); }
.pokedex-card.type-bg-ice    { background: var(--type-ice); }
.pokedex-card.type-bg-normal { background: var(--type-normal); }
.pokedex-card.type-bg-poison { background: var(--type-poison); }
.pokedex-card.type-bg-psychic { background: var(--type-psychic); }
.pokedex-card.type-bg-rock   { background: var(--type-rock); }
.pokedex-card.type-bg-steel  { background: var(--type-steel); }
.pokedex-card.type-bg-water  { background: var(--type-water); }


.pokedex-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    gap: 8px;
}

.pokedex-card-name {
    color: #fff;
    font-size: 1rem;
    font-weight: 500;
    text-shadow: 0 2px 6px rgba(0,0,0,0.25);
    letter-spacing: 0.1px;
    flex: 1 1 auto;
    min-width: 0;
    overflow: hidden;
    white-space: nowrap;
}
.pokedex-card-types {
    margin-bottom: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
}
.type-label {
    color: #fff;
    border-radius: 8px;
    padding: 2px 6px;
    font-size: 0.5rem;
    text-transform: uppercase;
    margin-right: 0;
    margin-bottom: 0;
    text-shadow: 0 2px 6px rgba(0,0,0,0.25);
    display: inline-block;
    box-shadow: 0 2px 6px rgba(0,0,0,0.25);
    font-weight: 500;
    margin: 2px 0;
    z-index: 19;
}
/* Pokémon-Icon: groß, mittig, unter Name/Typen */
.pokedex-card-img {
    position: absolute;
    bottom: -8px;
    right: -8px;
    top: auto;
    left: auto;
    width: 72px;
    height: auto;
    object-fit: contain;
    margin: 0;
    z-index: 18;
}

.pokedex-card-dex {
    color: rgba(0,0,0,0.37);
    font-size: 0.67rem;
    font-weight: 500;
    letter-spacing: 1px;
    pointer-events: none;
    user-select: none;
    flex: 0 0 auto;
    width: auto;
    min-width: 24px;
    text-align: right;
}

/* Hinweis: Für Dualtypen, die keine eigene CSS-Klasse haben, wird ein Inline-Style von JS gesetzt (linear-gradient). */

@media (max-width: 600px) {
    .pokedex-grid {
        gap: 10px;
        padding: 10px;
    }
}
